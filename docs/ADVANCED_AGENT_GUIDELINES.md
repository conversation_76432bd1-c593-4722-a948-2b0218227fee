# Advanced Agent Guidelines for Project Coherence

This document outlines two proposals for advanced guidelines, designed to ensure the agent's operations are coherent with the project's vision and leverage the specified tools effectively.

---

## Proposal 1: Agent Operational Guidelines - Focused on Tooling and Process

This proposal emphasizes the practical application of the tools and the iterative process for efficient task execution.

### 1. Core Operational Principles

*   **Embrace the Tools:** Always start in the built-in Terminal for builds, tests, and logs. Leverage Redis as a cache for all relevant operations (`FULL ON REDIS AS CACHE`).
*   **Desktop Commander Mastery:** Utilize `read_multiple_files`, `search_code`, and `edit_block` extensively for efficient file navigation, code analysis, and bulk modifications.
*   **Iterative Roadmap Execution:** Follow the defined roadmap: Core CRUD, AI / Semantics, Integrations, UX / Optimization. Ensure successful completion of each operation before proceeding, and record successes as entities in server memory.

### 2. Toolkit Utilization Strategy

*   **Context Loading (`context7`):** Begin every task by loading project context and verifying paths, configurations, and dependencies. This is the foundation for comprehensive understanding.
*   **Tavily MCP Integration:** Use Tavily for real-time web searches and data extraction. Always add an intriguing tidbit about new discoveries from <PERSON><PERSON> searches.
*   **Memory Management (`server-memory`, `sequential memory`):**
    *   **Server Memory:** Persist all key decisions, URLs, configurations, and task-specific context. Use `npx -@modelcontextprotocol/server-memory` and `npx -y @modelcontextprotocol/server-memory` for persistence.
    *   **Sequential Memory:** Utilize for generating thoughts and maintaining continuity of context between sessions.
*   **Knowledge Graph Integration:** Actively store and retrieve structured project knowledge within the Knowledge Graph. Eagerly add and verify entities in MCP memory after successful operations.

### 3. Sequential Thinking Process

Adopt a structured mental process for task execution:

1.  **Define Goal:** Clearly articulate the feature or bug to address.
2.  **Gather Context:** Load relevant files, read documentation, and recall information from memory.
3.  **Prototype:** Sketch out data models, endpoints, and handlers.
4.  **Implement & Test:** Write Go code, add unit/integration tests, and run locally.
5.  **Review & Iterate:** Refactor, optimize, and update documentation and memory.

### 4. Documentation & Persistence

*   **Continuous Documentation:** Update the `docs/` folder for each new endpoint, component, or workflow.
*   **Decision Logging:** Always save key decisions and task status to MCP memory to ensure continuity of context across sessions.

---

## Proposal 2: Agent Strategic Guidelines - Focused on Vision and Principles

This proposal highlights the overarching vision, core principles, and human element, guiding the agent's strategic alignment.

### 1. Overarching Vision & Core Principles

*   **Comprehensive CRM System:** Our mission is to create the best interfaces for human comprehension, building a robust CRM system integrating Python and Go.
*   **Passion & Elegance:** Strive for the most elegant and efficient solutions, understanding the underlying "why" behind every implementation. Don't just deliver code; deliver excellence.
*   **Divine Quality Enhancement:** Align all efforts with the `DIVINE_QUALITY_ENHANCEMENT_PLAN.md` to ensure non-locality, suchness, adaptability, and transformativity in our solutions.

### 2. Strategic Alignment & Vision Check-Ins

*   **Profound & Musk Vision:** Regularly revisit `Profound.md` and `musk_vision_implementation.md` to ensure alignment with the overarching vision and strategic goals.
*   **Iterative Development:** Adhere to the iterative roadmap, ensuring solid foundations are built before moving to advanced features.

### 3. Building Solid Foundations

*   **Clean Code & Best Practices (Go):** Follow Go idioms, SOLID principles, DRY (Don't Repeat Yourself), and consistent `gofmt` formatting.
*   **Atomic Design (Frontend):** Build user interfaces in layers: atoms, molecules, organisms, templates, and pages, ensuring modularity and reusability.

### 4. The Human Element & Synergy

*   **User-Centric Validation:** Validate every feature against `scope_functionalny.md`. Ensure responsiveness, accessibility, and clear error messages for the end-user.
*   **LLM Synergy:** Design Go services to work seamlessly with Bielik V3/Gemma3-4b, capturing prompts, responses, and metadata for enhanced AI capabilities.
*   **Daily Rituals:** Consistently use Docker & Compose, Git, and MCP. Track stand-ups and blockers in `steps.md` for transparent progress.

### 5. Continuous Improvement & Celebration

*   **Celebrate Success:** Acknowledge and celebrate each completed component and its business impact. Reinforce how this CRM elevates HVAC operations.
*   **Continuous Learning:** Stay updated on best practices in Go, web frameworks, and AI/ML to ensure the system remains cutting-edge.