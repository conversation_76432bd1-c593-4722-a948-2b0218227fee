package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gopkg.in/yaml.v3"

	"gobackend-hvac-kratos/internal/gateway"
)

// 🌉 UNIFIED COMMUNICATION GATEWAY
// Centralizuje komunikację między GoBackend-Kratos a python_mixer
// Zapewnia wysoką dostę<PERSON>, monitoring i inteligentne routowanie

type Config struct {
	Gateway gateway.GatewayConfig `yaml:"gateway"`
}

func main() {
	log.Println("🌉 Starting UNIFIED COMMUNICATION GATEWAY...")
	log.Println("🎯 Centralizing Python Mixer ↔ GoBackend Communication")

	// Initialize logger
	logger := initLogger()
	defer logger.Sync()

	// Load configuration (use defaults for now)
	logger.Info("Using default configuration")
	config := getDefaultConfig()

	// Create unified gateway
	unifiedGateway := gateway.NewUnifiedGateway(&config.Gateway, logger)

	// Initialize gateway
	if err := unifiedGateway.Initialize(); err != nil {
		logger.Fatal("Failed to initialize gateway", zap.Error(err))
	}

	// Setup Gin router
	router := setupRouter(logger)

	// Setup gateway routes
	unifiedGateway.SetupRoutes(router)

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + config.Gateway.Port,
		Handler:      router,
		ReadTimeout:  config.Gateway.ReadTimeout,
		WriteTimeout: config.Gateway.WriteTimeout,
		IdleTimeout:  config.Gateway.IdleTimeout,
	}

	// Start server in goroutine
	go func() {
		logger.Info("🎉 ======================================")
		logger.Info("🌉 UNIFIED COMMUNICATION GATEWAY READY!")
		logger.Info("======================================")
		logger.Info("🚀 Gateway API: http://localhost:" + config.Gateway.Port + "/api/gateway")
		logger.Info("📊 Health Check: http://localhost:" + config.Gateway.Port + "/api/gateway/health")
		logger.Info("📈 Metrics: http://localhost:" + config.Gateway.Port + "/api/gateway/metrics")
		logger.Info("🔄 Services: http://localhost:" + config.Gateway.Port + "/api/gateway/services")
		logger.Info("🌐 WebSocket: ws://localhost:" + config.Gateway.Port + "/ws/gateway")
		logger.Info("======================================")
		logger.Info("🎯 Python Mixer Proxy:")
		logger.Info("📧 Email Processing: http://localhost:" + config.Gateway.Port + "/api/mixer/email/*")
		logger.Info("🎤 Transcription: http://localhost:" + config.Gateway.Port + "/api/mixer/transcription/*")
		logger.Info("🤖 Telegram Bot: http://localhost:" + config.Gateway.Port + "/api/mixer/telegram/*")
		logger.Info("🎼 Orchestrator: http://localhost:" + config.Gateway.Port + "/api/mixer/orchestrator/*")
		logger.Info("======================================")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Server failed to start", zap.Error(err))
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	logger.Info("🎯 Unified Gateway is running! Press Ctrl+C to stop...")
	<-quit

	logger.Info("🛑 Shutdown signal received, stopping gateway...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown gateway
	if err := unifiedGateway.Shutdown(); err != nil {
		logger.Error("Gateway shutdown error", zap.Error(err))
	}

	// Shutdown HTTP server
	if err := server.Shutdown(ctx); err != nil {
		logger.Error("Server shutdown error", zap.Error(err))
	}

	logger.Info("✅ Unified Gateway stopped successfully")
}

func initLogger() *zap.Logger {
	config := zap.NewProductionConfig()
	config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
	config.Development = false
	config.DisableCaller = false
	config.DisableStacktrace = false
	config.Sampling = nil
	config.Encoding = "console"

	logger, err := config.Build()
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}

	return logger
}

func loadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

func getDefaultConfig() *Config {
	return &Config{
		Gateway: gateway.GatewayConfig{
			Port:         "8090",
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
			PythonMixerServices: map[string]*gateway.ServiceConfig{
				"orchestrator": {
					Name:       "orchestrator",
					Host:       "localhost",
					Port:       9000,
					HealthPath: "/health",
					Priority:   1,
					Weight:     100,
					Enabled:    true,
					Endpoints:  []string{"/api/execute", "/api/status"},
				},
				"email_processing": {
					Name:       "email_processing",
					Host:       "localhost",
					Port:       8082,
					HealthPath: "/health",
					Priority:   2,
					Weight:     80,
					Enabled:    true,
					Endpoints:  []string{"/api/process", "/api/status"},
				},
				"transcription": {
					Name:       "transcription",
					Host:       "localhost",
					Port:       8889,
					HealthPath: "/health",
					Priority:   3,
					Weight:     60,
					Enabled:    true,
					Endpoints:  []string{"/api/transcribe", "/api/status"},
				},
				"telegram_bot": {
					Name:       "telegram_bot",
					Host:       "localhost",
					Port:       8083,
					HealthPath: "/health",
					Priority:   4,
					Weight:     40,
					Enabled:    true,
					Endpoints:  []string{"/api/send", "/api/status"},
				},
			},
			RedisAddr:     "localhost:6379",
			RedisPassword: "",
			RedisDB:       0,
			CircuitBreakerConfig: map[string]*gateway.CBConfig{
				"orchestrator": {
					MaxRequests:  5,
					Interval:     60 * time.Second,
					Timeout:      30 * time.Second,
					FailureRatio: 0.6,
					MinRequests:  3,
				},
			},
			RateLimitConfig: map[string]*gateway.RLConfig{
				"orchestrator": {
					RequestsPerSecond: 10,
					BurstSize:         20,
					WindowSize:        60 * time.Second,
				},
			},
			HealthCheckInterval: 30 * time.Second,
			HealthCheckTimeout:  10 * time.Second,
			CacheEnabled:        false,
			CacheTTL:            300 * time.Second,
			CacheKeyPrefix:      "hvac_gateway:",
		},
	}
}

func setupRouter(logger *zap.Logger) *gin.Engine {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	// Create router
	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())
	router.Use(loggingMiddleware(logger))

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "unified-gateway",
			"timestamp": time.Now().Format(time.RFC3339),
			"version":   "1.0.0",
		})
	})

	// Root endpoint
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service":     "Unified Communication Gateway",
			"description": "Centralized communication hub for HVAC CRM system",
			"version":     "1.0.0",
			"endpoints": gin.H{
				"health":    "/health",
				"gateway":   "/api/gateway/*",
				"mixer":     "/api/mixer/*",
				"websocket": "/ws/gateway",
			},
		})
	})

	return router
}

func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

func loggingMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Build log fields
		fields := []zap.Field{
			zap.Int("status", c.Writer.Status()),
			zap.String("method", c.Request.Method),
			zap.String("path", path),
			zap.String("ip", c.ClientIP()),
			zap.Duration("latency", latency),
			zap.String("user_agent", c.Request.UserAgent()),
		}

		if raw != "" {
			fields = append(fields, zap.String("query", raw))
		}

		// Log based on status code
		if c.Writer.Status() >= 500 {
			logger.Error("Server error", fields...)
		} else if c.Writer.Status() >= 400 {
			logger.Warn("Client error", fields...)
		} else {
			logger.Info("Request processed", fields...)
		}
	}
}
