package gateway

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ServiceRegistry manages service discovery and health tracking
type ServiceRegistry struct {
	services map[string]*ServiceConfig
	health   map[string]*ServiceHealth
	mutex    sync.RWMutex
	logger   *zap.Logger
}

type ServiceHealth struct {
	Status       string    `json:"status"` // healthy, unhealthy, unknown
	LastCheck    time.Time `json:"last_check"`
	ResponseTime float64   `json:"response_time"`
	ErrorCount   int       `json:"error_count"`
	Uptime       float64   `json:"uptime"`
}

// NewServiceRegistry creates a new service registry
func NewServiceRegistry(services map[string]*ServiceConfig, logger *zap.Logger) *ServiceRegistry {
	registry := &ServiceRegistry{
		services: make(map[string]*ServiceConfig),
		health:   make(map[string]*ServiceHealth),
		logger:   logger,
	}

	// Initialize services
	for name, config := range services {
		registry.services[name] = config
		registry.health[name] = &ServiceHealth{
			Status:    "unknown",
			LastCheck: time.Now(),
		}
	}

	return registry
}

// GetService returns a service configuration by name
func (sr *ServiceRegistry) GetService(name string) (*ServiceConfig, error) {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	service, exists := sr.services[name]
	if !exists {
		return nil, ErrServiceNotFound
	}

	return service, nil
}

// GetHealthyServices returns all healthy services
func (sr *ServiceRegistry) GetHealthyServices() map[string]*ServiceConfig {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	healthy := make(map[string]*ServiceConfig)
	for name, service := range sr.services {
		if health, exists := sr.health[name]; exists && health.Status == "healthy" {
			healthy[name] = service
		}
	}

	return healthy
}

// UpdateHealth updates the health status of a service
func (sr *ServiceRegistry) UpdateHealth(name string, status string, responseTime float64) {
	sr.mutex.Lock()
	defer sr.mutex.Unlock()

	if health, exists := sr.health[name]; exists {
		health.Status = status
		health.LastCheck = time.Now()
		health.ResponseTime = responseTime

		if status != "healthy" {
			health.ErrorCount++
		}
	}
}

// GetAllServices returns all registered services
func (sr *ServiceRegistry) GetAllServices() map[string]*ServiceConfig {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	services := make(map[string]*ServiceConfig)
	for name, service := range sr.services {
		services[name] = service
	}

	return services
}

// GetServiceHealth returns health information for a service
func (sr *ServiceRegistry) GetServiceHealth(name string) (*ServiceHealth, error) {
	sr.mutex.RLock()
	defer sr.mutex.RUnlock()

	health, exists := sr.health[name]
	if !exists {
		return nil, ErrServiceNotFound
	}

	return health, nil
}

// MessageRouter handles intelligent message routing
type MessageRouter struct {
	registry *ServiceRegistry
	logger   *zap.Logger
}

// NewMessageRouter creates a new message router
func NewMessageRouter(registry *ServiceRegistry, logger *zap.Logger) *MessageRouter {
	return &MessageRouter{
		registry: registry,
		logger:   logger,
	}
}

// RouteMessage routes a message to the appropriate service
func (mr *MessageRouter) RouteMessage(messageType string, data interface{}) (*ServiceConfig, error) {
	// Route based on message type
	var serviceName string

	switch messageType {
	case "transcription_request", "transcription_status":
		serviceName = "transcription"
	case "email_process", "email_status":
		serviceName = "email_processing"
	case "telegram_send", "telegram_status":
		serviceName = "telegram_bot"
	case "orchestrator_execute", "orchestrator_status":
		serviceName = "orchestrator"
	default:
		return nil, ErrUnknownMessageType
	}

	return mr.registry.GetService(serviceName)
}

// LoadBalancer provides load balancing capabilities
type LoadBalancer struct {
	registry *ServiceRegistry
	logger   *zap.Logger
}

// NewLoadBalancer creates a new load balancer
func NewLoadBalancer(registry *ServiceRegistry, logger *zap.Logger) *LoadBalancer {
	return &LoadBalancer{
		registry: registry,
		logger:   logger,
	}
}

// GetService returns the best available service instance
func (lb *LoadBalancer) GetService(serviceName string) (*ServiceConfig, error) {
	// For now, just return the service if it's healthy
	// In the future, implement weighted round-robin, least connections, etc.

	service, err := lb.registry.GetService(serviceName)
	if err != nil {
		return nil, err
	}

	health, err := lb.registry.GetServiceHealth(serviceName)
	if err != nil {
		return nil, err
	}

	if health.Status != "healthy" {
		return nil, ErrServiceUnhealthy
	}

	return service, nil
}

// GatewayHealthChecker monitors service health
type GatewayHealthChecker struct {
	registry *ServiceRegistry
	config   *GatewayConfig
	logger   *zap.Logger
}

// NewGatewayHealthChecker creates a new health checker
func NewGatewayHealthChecker(registry *ServiceRegistry, config *GatewayConfig, logger *zap.Logger) *GatewayHealthChecker {
	return &GatewayHealthChecker{
		registry: registry,
		config:   config,
		logger:   logger,
	}
}

// CheckService checks the health of a specific service
func (hc *GatewayHealthChecker) CheckService(serviceName string) error {
	service, err := hc.registry.GetService(serviceName)
	if err != nil {
		return err
	}

	// Implement actual health check logic here
	// For now, just mark as healthy
	hc.registry.UpdateHealth(serviceName, "healthy", 0.1)

	hc.logger.Debug("Service health checked",
		zap.String("service", serviceName),
		zap.String("host", service.Host),
		zap.Int("port", service.Port),
	)

	return nil
}

// CheckAllServices checks health of all registered services
func (hc *GatewayHealthChecker) CheckAllServices() {
	services := hc.registry.GetAllServices()

	for name := range services {
		if err := hc.CheckService(name); err != nil {
			hc.logger.Error("Health check failed",
				zap.String("service", name),
				zap.Error(err),
			)
		}
	}
}

// Custom errors
var (
	ErrServiceNotFound    = fmt.Errorf("service not found")
	ErrServiceUnhealthy   = fmt.Errorf("service is unhealthy")
	ErrUnknownMessageType = fmt.Errorf("unknown message type")
)
