package gateway

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gorilla/websocket"
	"github.com/sony/gobreaker"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// 🌉 UNIFIED COMMUNICATION GATEWAY
// Centralizuje całą komunikację między GoBackend-Kratos a python_mixer
// Zapewnia wysoką dostęp<PERSON>ć, monitoring i inteligentne routowanie

type UnifiedGateway struct {
	// Core components
	logger      *zap.Logger
	httpClient  *http.Client
	redisClient *redis.Client

	// Routing and load balancing
	serviceRegistry *ServiceRegistry
	messageRouter   *MessageRouter
	loadBalancer    *LoadBalancer

	// Resilience patterns
	circuitBreakers map[string]*gobreaker.CircuitBreaker
	rateLimiters    map[string]*rate.Limiter

	// Real-time communication
	wsUpgrader    websocket.Upgrader
	activeWSConns map[string]*websocket.Conn
	wsMutex       sync.RWMutex

	// Monitoring and metrics
	metrics       *GatewayMetrics
	healthChecker *GatewayHealthChecker

	// Configuration
	config *GatewayConfig

	// Lifecycle management
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

type GatewayConfig struct {
	// Gateway settings
	Port         string        `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`

	// Python Mixer services
	PythonMixerServices map[string]*ServiceConfig `json:"python_mixer_services"`

	// Redis configuration
	RedisAddr     string `json:"redis_addr"`
	RedisPassword string `json:"redis_password"`
	RedisDB       int    `json:"redis_db"`

	// Circuit breaker settings
	CircuitBreakerConfig map[string]*CBConfig `json:"circuit_breaker_config"`

	// Rate limiting
	RateLimitConfig map[string]*RLConfig `json:"rate_limit_config"`

	// Health check settings
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`

	// Caching settings
	CacheEnabled   bool          `json:"cache_enabled"`
	CacheTTL       time.Duration `json:"cache_ttl"`
	CacheKeyPrefix string        `json:"cache_key_prefix"`
}

type ServiceConfig struct {
	Name       string   `json:"name"`
	Host       string   `json:"host"`
	Port       int      `json:"port"`
	HealthPath string   `json:"health_path"`
	Priority   int      `json:"priority"`
	Weight     int      `json:"weight"`
	Endpoints  []string `json:"endpoints"`
	Enabled    bool     `json:"enabled"`
}

type CBConfig struct {
	MaxRequests  uint32        `json:"max_requests"`
	Interval     time.Duration `json:"interval"`
	Timeout      time.Duration `json:"timeout"`
	FailureRatio float64       `json:"failure_ratio"`
	MinRequests  uint32        `json:"min_requests"`
}

type RLConfig struct {
	RequestsPerSecond int           `json:"requests_per_second"`
	BurstSize         int           `json:"burst_size"`
	WindowSize        time.Duration `json:"window_size"`
}

type GatewayMetrics struct {
	// Request metrics
	TotalRequests  int64   `json:"total_requests"`
	SuccessfulReqs int64   `json:"successful_requests"`
	FailedRequests int64   `json:"failed_requests"`
	AverageLatency float64 `json:"average_latency"`

	// Service metrics
	ServiceCalls     map[string]int64   `json:"service_calls"`
	ServiceLatencies map[string]float64 `json:"service_latencies"`
	ServiceErrors    map[string]int64   `json:"service_errors"`

	// Circuit breaker metrics
	CBTrips      map[string]int64 `json:"circuit_breaker_trips"`
	CBRecoveries map[string]int64 `json:"circuit_breaker_recoveries"`

	// Rate limiting metrics
	RateLimitHits map[string]int64 `json:"rate_limit_hits"`

	// Cache metrics
	CacheHits   int64 `json:"cache_hits"`
	CacheMisses int64 `json:"cache_misses"`
	CacheErrors int64 `json:"cache_errors"`

	// WebSocket metrics
	ActiveConnections int64 `json:"active_connections"`
	WSMessages        int64 `json:"websocket_messages"`

	mutex sync.RWMutex
}

// NewUnifiedGateway creates a new unified communication gateway
func NewUnifiedGateway(config *GatewayConfig, logger *zap.Logger) *UnifiedGateway {
	ctx, cancel := context.WithCancel(context.Background())

	gateway := &UnifiedGateway{
		logger:          logger,
		config:          config,
		ctx:             ctx,
		cancel:          cancel,
		circuitBreakers: make(map[string]*gobreaker.CircuitBreaker),
		rateLimiters:    make(map[string]*rate.Limiter),
		activeWSConns:   make(map[string]*websocket.Conn),
		metrics: &GatewayMetrics{
			ServiceCalls:     make(map[string]int64),
			ServiceLatencies: make(map[string]float64),
			ServiceErrors:    make(map[string]int64),
			CBTrips:          make(map[string]int64),
			CBRecoveries:     make(map[string]int64),
			RateLimitHits:    make(map[string]int64),
		},
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Configure properly for production
			},
		},
	}

	// Initialize HTTP client
	gateway.httpClient = &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	return gateway
}

// Initialize starts the unified gateway
func (g *UnifiedGateway) Initialize() error {
	g.logger.Info("🌉 Initializing Unified Communication Gateway...")

	// Initialize Redis client
	if err := g.initializeRedis(); err != nil {
		return fmt.Errorf("failed to initialize Redis: %w", err)
	}

	// Initialize service registry
	g.serviceRegistry = NewServiceRegistry(g.config.PythonMixerServices, g.logger)

	// Initialize message router
	g.messageRouter = NewMessageRouter(g.serviceRegistry, g.logger)

	// Initialize load balancer
	g.loadBalancer = NewLoadBalancer(g.serviceRegistry, g.logger)

	// Initialize circuit breakers
	g.initializeCircuitBreakers()

	// Initialize rate limiters
	g.initializeRateLimiters()

	// Initialize health checker
	g.healthChecker = NewGatewayHealthChecker(g.serviceRegistry, g.config, g.logger)

	// Start background services
	g.startBackgroundServices()

	g.logger.Info("✅ Unified Communication Gateway initialized successfully")
	return nil
}

// initializeRedis sets up Redis connection
func (g *UnifiedGateway) initializeRedis() error {
	if !g.config.CacheEnabled {
		g.logger.Info("📦 Redis caching disabled")
		return nil
	}

	g.redisClient = redis.NewClient(&redis.Options{
		Addr:     g.config.RedisAddr,
		Password: g.config.RedisPassword,
		DB:       g.config.RedisDB,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(g.ctx, 5*time.Second)
	defer cancel()

	if err := g.redisClient.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis connection failed: %w", err)
	}

	g.logger.Info("✅ Redis connection established", zap.String("addr", g.config.RedisAddr))
	return nil
}

// initializeCircuitBreakers sets up circuit breakers for all services
func (g *UnifiedGateway) initializeCircuitBreakers() {
	for serviceName, cbConfig := range g.config.CircuitBreakerConfig {
		settings := gobreaker.Settings{
			Name:        fmt.Sprintf("gateway_%s", serviceName),
			MaxRequests: cbConfig.MaxRequests,
			Interval:    cbConfig.Interval,
			Timeout:     cbConfig.Timeout,
			ReadyToTrip: func(counts gobreaker.Counts) bool {
				failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
				return counts.Requests >= cbConfig.MinRequests && failureRatio >= cbConfig.FailureRatio
			},
			OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
				g.logger.Info("Circuit breaker state changed",
					zap.String("service", serviceName),
					zap.String("from", from.String()),
					zap.String("to", to.String()),
				)

				g.metrics.mutex.Lock()
				if to == gobreaker.StateOpen {
					g.metrics.CBTrips[serviceName]++
				} else if to == gobreaker.StateClosed && from == gobreaker.StateHalfOpen {
					g.metrics.CBRecoveries[serviceName]++
				}
				g.metrics.mutex.Unlock()
			},
		}

		g.circuitBreakers[serviceName] = gobreaker.NewCircuitBreaker(settings)
	}

	g.logger.Info("✅ Circuit breakers initialized", zap.Int("count", len(g.circuitBreakers)))
}

// initializeRateLimiters sets up rate limiters for all services
func (g *UnifiedGateway) initializeRateLimiters() {
	for serviceName, rlConfig := range g.config.RateLimitConfig {
		limiter := rate.NewLimiter(
			rate.Limit(rlConfig.RequestsPerSecond),
			rlConfig.BurstSize,
		)
		g.rateLimiters[serviceName] = limiter
	}

	g.logger.Info("✅ Rate limiters initialized", zap.Int("count", len(g.rateLimiters)))
}

// startBackgroundServices starts all background monitoring services
func (g *UnifiedGateway) startBackgroundServices() {
	// Start health monitoring
	g.wg.Add(1)
	go g.healthMonitor()

	// Start metrics collector
	g.wg.Add(1)
	go g.metricsCollector()

	// Start service discovery
	g.wg.Add(1)
	go g.serviceDiscovery()

	g.logger.Info("✅ Background services started")
}

// SetupRoutes configures all gateway routes
func (g *UnifiedGateway) SetupRoutes(engine *gin.Engine) {
	// Gateway API group
	api := engine.Group("/api/gateway")
	{
		// Health and status endpoints
		api.GET("/health", g.handleHealth)
		api.GET("/metrics", g.handleMetrics)
		api.GET("/status", g.handleStatus)

		// Service management
		api.GET("/services", g.handleListServices)
		api.GET("/services/:service/health", g.handleServiceHealth)
		api.POST("/services/:service/reload", g.handleServiceReload)

		// Circuit breaker management
		api.GET("/circuit-breakers", g.handleListCircuitBreakers)
		api.POST("/circuit-breakers/:service/reset", g.handleResetCircuitBreaker)

		// Cache management
		api.GET("/cache/stats", g.handleCacheStats)
		api.DELETE("/cache/:key", g.handleCacheDelete)
		api.DELETE("/cache", g.handleCacheClear)
	}

	// Python Mixer proxy routes
	mixer := engine.Group("/api/mixer")
	{
		// Transcription service
		mixer.POST("/transcription/process", g.proxyToService("transcription"))
		mixer.GET("/transcription/status/:id", g.proxyToService("transcription"))

		// Email processing service
		mixer.POST("/email/process", g.proxyToService("email_processing"))
		mixer.GET("/email/status/:id", g.proxyToService("email_processing"))

		// Telegram bot service
		mixer.POST("/telegram/send", g.proxyToService("telegram_bot"))
		mixer.GET("/telegram/status", g.proxyToService("telegram_bot"))

		// Orchestrator service
		mixer.POST("/orchestrator/execute", g.proxyToService("orchestrator"))
		mixer.GET("/orchestrator/status", g.proxyToService("orchestrator"))
	}

	// WebSocket endpoint for real-time communication
	engine.GET("/ws/gateway", g.handleWebSocket)

	g.logger.Info("✅ Gateway routes configured")
}

// proxyToService creates a proxy handler for a specific service
func (g *UnifiedGateway) proxyToService(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()

		// Update metrics
		g.metrics.mutex.Lock()
		g.metrics.TotalRequests++
		g.metrics.ServiceCalls[serviceName]++
		g.metrics.mutex.Unlock()

		// Check rate limiting
		if limiter, exists := g.rateLimiters[serviceName]; exists {
			if !limiter.Allow() {
				g.metrics.mutex.Lock()
				g.metrics.RateLimitHits[serviceName]++
				g.metrics.mutex.Unlock()

				c.JSON(http.StatusTooManyRequests, gin.H{
					"error":   "Rate limit exceeded",
					"service": serviceName,
				})
				return
			}
		}

		// Get service instance
		service, err := g.loadBalancer.GetService(serviceName)
		if err != nil {
			g.logger.Error("Service not available",
				zap.String("service", serviceName),
				zap.Error(err),
			)

			g.metrics.mutex.Lock()
			g.metrics.FailedRequests++
			g.metrics.ServiceErrors[serviceName]++
			g.metrics.mutex.Unlock()

			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service unavailable",
				"service": serviceName,
			})
			return
		}

		// Execute with circuit breaker
		cb := g.circuitBreakers[serviceName]
		result, err := cb.Execute(func() (interface{}, error) {
			return g.forwardRequest(c, service)
		})

		// Calculate latency
		latency := time.Since(start).Seconds()

		// Update metrics
		g.metrics.mutex.Lock()
		if err != nil {
			g.metrics.FailedRequests++
			g.metrics.ServiceErrors[serviceName]++
		} else {
			g.metrics.SuccessfulReqs++
		}

		// Update average latency
		currentCalls := g.metrics.ServiceCalls[serviceName]
		currentLatency := g.metrics.ServiceLatencies[serviceName]
		g.metrics.ServiceLatencies[serviceName] = (currentLatency*float64(currentCalls-1) + latency) / float64(currentCalls)

		// Update overall average latency
		g.metrics.AverageLatency = (g.metrics.AverageLatency*float64(g.metrics.TotalRequests-1) + latency) / float64(g.metrics.TotalRequests)
		g.metrics.mutex.Unlock()

		if err != nil {
			g.logger.Error("Request failed",
				zap.String("service", serviceName),
				zap.Error(err),
				zap.Float64("latency", latency),
			)

			if err == gobreaker.ErrOpenState {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error":   "Circuit breaker open",
					"service": serviceName,
				})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error":   "Request failed",
					"service": serviceName,
				})
			}
			return
		}

		// Return successful result
		if response, ok := result.(*http.Response); ok {
			g.forwardResponse(c, response)
		}
	}
}

// forwardRequest forwards the request to the target service
func (g *UnifiedGateway) forwardRequest(c *gin.Context, service *ServiceConfig) (*http.Response, error) {
	// Build target URL
	targetURL := fmt.Sprintf("http://%s:%d%s", service.Host, service.Port, c.Request.URL.Path)

	// Create new request
	req, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, targetURL, c.Request.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Copy headers
	for key, values := range c.Request.Header {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// Add gateway headers
	req.Header.Set("X-Gateway-Service", service.Name)
	req.Header.Set("X-Gateway-Timestamp", time.Now().Format(time.RFC3339))

	// Execute request
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}

	return resp, nil
}

// forwardResponse forwards the response back to the client
func (g *UnifiedGateway) forwardResponse(c *gin.Context, resp *http.Response) {
	defer resp.Body.Close()

	// Copy headers
	for key, values := range resp.Header {
		for _, value := range values {
			c.Header(key, value)
		}
	}

	// Set status code
	c.Status(resp.StatusCode)

	// Copy body
	if resp.Body != nil {
		var buf []byte
		buf = make([]byte, 32*1024) // 32KB buffer
		for {
			n, err := resp.Body.Read(buf)
			if n > 0 {
				c.Writer.Write(buf[:n])
			}
			if err != nil {
				break
			}
		}
	}
}

// Background service methods
func (g *UnifiedGateway) healthMonitor() {
	defer g.wg.Done()

	ticker := time.NewTicker(g.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-g.ctx.Done():
			return
		case <-ticker.C:
			g.healthChecker.CheckAllServices()
		}
	}
}

func (g *UnifiedGateway) metricsCollector() {
	defer g.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-g.ctx.Done():
			return
		case <-ticker.C:
			g.collectMetrics()
		}
	}
}

func (g *UnifiedGateway) serviceDiscovery() {
	defer g.wg.Done()

	ticker := time.NewTicker(60 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-g.ctx.Done():
			return
		case <-ticker.C:
			g.discoverServices()
		}
	}
}

func (g *UnifiedGateway) collectMetrics() {
	// Collect and log metrics
	g.metrics.mutex.RLock()
	defer g.metrics.mutex.RUnlock()

	g.logger.Info("Gateway metrics",
		zap.Int64("total_requests", g.metrics.TotalRequests),
		zap.Int64("successful_requests", g.metrics.SuccessfulReqs),
		zap.Int64("failed_requests", g.metrics.FailedRequests),
		zap.Float64("average_latency", g.metrics.AverageLatency),
		zap.Int64("cache_hits", g.metrics.CacheHits),
		zap.Int64("cache_misses", g.metrics.CacheMisses),
	)
}

func (g *UnifiedGateway) discoverServices() {
	// Service discovery logic
	g.logger.Debug("Running service discovery")

	// Check if new services are available
	// Update service registry
	// This is a placeholder for future implementation
}

// HTTP Handler methods
func (g *UnifiedGateway) handleHealth(c *gin.Context) {
	health := gin.H{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
		"services":  g.getServicesHealth(),
	}

	c.JSON(http.StatusOK, health)
}

func (g *UnifiedGateway) handleMetrics(c *gin.Context) {
	g.metrics.mutex.RLock()
	defer g.metrics.mutex.RUnlock()

	c.JSON(http.StatusOK, g.metrics)
}

func (g *UnifiedGateway) handleStatus(c *gin.Context) {
	status := gin.H{
		"gateway":            "running",
		"uptime":             time.Since(time.Now()).String(), // This should track actual uptime
		"services":           len(g.serviceRegistry.GetAllServices()),
		"active_connections": g.metrics.ActiveConnections,
	}

	c.JSON(http.StatusOK, status)
}

func (g *UnifiedGateway) handleListServices(c *gin.Context) {
	services := g.serviceRegistry.GetAllServices()
	c.JSON(http.StatusOK, gin.H{"services": services})
}

func (g *UnifiedGateway) handleServiceHealth(c *gin.Context) {
	serviceName := c.Param("service")

	health, err := g.serviceRegistry.GetServiceHealth(serviceName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	c.JSON(http.StatusOK, health)
}

func (g *UnifiedGateway) handleServiceReload(c *gin.Context) {
	serviceName := c.Param("service")

	// Reload service configuration
	err := g.healthChecker.CheckService(serviceName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Service reloaded successfully"})
}

func (g *UnifiedGateway) handleListCircuitBreakers(c *gin.Context) {
	breakers := make(map[string]interface{})

	for name, cb := range g.circuitBreakers {
		breakers[name] = gin.H{
			"state":  cb.State().String(),
			"counts": cb.Counts(),
		}
	}

	c.JSON(http.StatusOK, gin.H{"circuit_breakers": breakers})
}

func (g *UnifiedGateway) handleResetCircuitBreaker(c *gin.Context) {
	serviceName := c.Param("service")

	if cb, exists := g.circuitBreakers[serviceName]; exists {
		// Reset circuit breaker (this is a placeholder - gobreaker doesn't have public reset)
		g.logger.Info("Circuit breaker reset requested", zap.String("service", serviceName))
		_ = cb // Use cb to avoid unused variable error
		c.JSON(http.StatusOK, gin.H{"message": "Circuit breaker reset"})
	} else {
		c.JSON(http.StatusNotFound, gin.H{"error": "Circuit breaker not found"})
	}
}

func (g *UnifiedGateway) handleCacheStats(c *gin.Context) {
	if g.redisClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Cache not enabled"})
		return
	}

	stats := gin.H{
		"hits":   g.metrics.CacheHits,
		"misses": g.metrics.CacheMisses,
		"errors": g.metrics.CacheErrors,
	}

	c.JSON(http.StatusOK, stats)
}

func (g *UnifiedGateway) handleCacheDelete(c *gin.Context) {
	if g.redisClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Cache not enabled"})
		return
	}

	key := c.Param("key")
	err := g.redisClient.Del(g.ctx, g.config.CacheKeyPrefix+key).Err()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Cache key deleted"})
}

func (g *UnifiedGateway) handleCacheClear(c *gin.Context) {
	if g.redisClient == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Cache not enabled"})
		return
	}

	pattern := g.config.CacheKeyPrefix + "*"
	keys, err := g.redisClient.Keys(g.ctx, pattern).Result()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if len(keys) > 0 {
		err = g.redisClient.Del(g.ctx, keys...).Err()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": fmt.Sprintf("Cleared %d cache keys", len(keys))})
}

func (g *UnifiedGateway) getServicesHealth() map[string]interface{} {
	services := g.serviceRegistry.GetAllServices()
	health := make(map[string]interface{})

	for name := range services {
		if serviceHealth, err := g.serviceRegistry.GetServiceHealth(name); err == nil {
			health[name] = serviceHealth
		}
	}

	return health
}

func (g *UnifiedGateway) handleWebSocket(c *gin.Context) {
	conn, err := g.wsUpgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		g.logger.Error("WebSocket upgrade failed", zap.Error(err))
		return
	}
	defer conn.Close()

	// Generate connection ID
	connID := fmt.Sprintf("ws_%d", time.Now().UnixNano())

	// Store connection
	g.wsMutex.Lock()
	g.activeWSConns[connID] = conn
	g.metrics.ActiveConnections++
	g.wsMutex.Unlock()

	// Remove connection on exit
	defer func() {
		g.wsMutex.Lock()
		delete(g.activeWSConns, connID)
		g.metrics.ActiveConnections--
		g.wsMutex.Unlock()
	}()

	g.logger.Info("WebSocket connection established", zap.String("conn_id", connID))

	// Handle messages
	for {
		var msg map[string]interface{}
		err := conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				g.logger.Error("WebSocket error", zap.Error(err))
			}
			break
		}

		g.metrics.mutex.Lock()
		g.metrics.WSMessages++
		g.metrics.mutex.Unlock()

		// Process WebSocket message
		g.processWebSocketMessage(conn, msg)
	}
}

func (g *UnifiedGateway) processWebSocketMessage(conn *websocket.Conn, msg map[string]interface{}) {
	msgType, ok := msg["type"].(string)
	if !ok {
		g.sendWebSocketError(conn, "Invalid message format")
		return
	}

	switch msgType {
	case "ping":
		g.sendWebSocketMessage(conn, map[string]interface{}{
			"type":      "pong",
			"timestamp": time.Now().Format(time.RFC3339),
		})
	case "get_metrics":
		g.metrics.mutex.RLock()
		metrics := *g.metrics
		g.metrics.mutex.RUnlock()

		g.sendWebSocketMessage(conn, map[string]interface{}{
			"type": "metrics",
			"data": metrics,
		})
	case "get_services":
		services := g.getServicesHealth()
		g.sendWebSocketMessage(conn, map[string]interface{}{
			"type": "services",
			"data": services,
		})
	default:
		g.sendWebSocketError(conn, "Unknown message type")
	}
}

func (g *UnifiedGateway) sendWebSocketMessage(conn *websocket.Conn, msg map[string]interface{}) {
	if err := conn.WriteJSON(msg); err != nil {
		g.logger.Error("Failed to send WebSocket message", zap.Error(err))
	}
}

func (g *UnifiedGateway) sendWebSocketError(conn *websocket.Conn, errorMsg string) {
	g.sendWebSocketMessage(conn, map[string]interface{}{
		"type":  "error",
		"error": errorMsg,
	})
}

// Shutdown gracefully shuts down the gateway
func (g *UnifiedGateway) Shutdown() error {
	g.logger.Info("🔄 Shutting down Unified Gateway...")

	// Cancel context
	g.cancel()

	// Close WebSocket connections
	g.wsMutex.Lock()
	for id, conn := range g.activeWSConns {
		conn.Close()
		delete(g.activeWSConns, id)
	}
	g.wsMutex.Unlock()

	// Close Redis connection
	if g.redisClient != nil {
		g.redisClient.Close()
	}

	// Wait for background services to finish
	g.wg.Wait()

	g.logger.Info("✅ Unified Gateway shut down successfully")
	return nil
}
