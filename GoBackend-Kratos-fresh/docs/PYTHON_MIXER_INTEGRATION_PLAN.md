# 🔗 PYTHON MIXER INTEGRATION PLAN

## 🎯 **PLAN INTEGRACJI Z UNIFIED GATEWAY**

Teraz gdy **Unified Communication Gateway** działa perfek<PERSON>jnie, czas na integrację z python_mixer!

## 📋 **ETAPY INTEGRACJI**

### **ETAP 1: Gateway Client dla Python Mixer**

#### 1.1 Stworzenie Gateway Client
```python
# python_mixer/core/gateway_client.py
import aiohttp
import asyncio
from typing import Dict, Any, Optional

class UnifiedGatewayClient:
    def __init__(self, gateway_url: str = "http://localhost:8090"):
        self.gateway_url = gateway_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def send_to_service(self, service: str, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Wyślij żądanie przez gateway do konkretnej usługi"""
        url = f"{self.gateway_url}/api/mixer/{service}/{endpoint}"
        
        async with self.session.post(url, json=data) as response:
            return await response.json()
    
    async def get_service_status(self, service: str) -> Dict[str, Any]:
        """Pobierz status usługi przez gateway"""
        url = f"{self.gateway_url}/api/mixer/{service}/status"
        
        async with self.session.get(url) as response:
            return await response.json()
    
    async def get_gateway_health(self) -> Dict[str, Any]:
        """Sprawdź health gateway"""
        url = f"{self.gateway_url}/api/gateway/health"
        
        async with self.session.get(url) as response:
            return await response.json()
```

#### 1.2 Aktualizacja Orchestratora
```python
# python_mixer/core/integration/orchestrator.py - UPDATE
from .gateway_client import UnifiedGatewayClient

class IntegrationOrchestrator:
    def __init__(self):
        self.gateway_client = UnifiedGatewayClient()
        # ... existing code
    
    async def process_transcription_request(self, email_data: Dict[str, Any]):
        """Przetwórz żądanie transkrypcji przez gateway"""
        async with self.gateway_client as client:
            result = await client.send_to_service(
                service="transcription",
                endpoint="process",
                data=email_data
            )
            return result
    
    async def process_email_analysis(self, email_data: Dict[str, Any]):
        """Przetwórz analizę email przez gateway"""
        async with self.gateway_client as client:
            result = await client.send_to_service(
                service="email_processing", 
                endpoint="process",
                data=email_data
            )
            return result
```

### **ETAP 2: Service Endpoints Update**

#### 2.1 Transcription Service
```python
# python_mixer/engines/transcription_engine.py - ADD ENDPOINTS
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

app = FastAPI()

class TranscriptionRequest(BaseModel):
    email_id: str
    file_path: str
    language: str = "pl"

@app.post("/api/transcribe")
async def transcribe_audio(request: TranscriptionRequest):
    """Endpoint dla gateway - transkrypcja audio"""
    try:
        # Existing transcription logic
        result = await process_transcription(request.file_path, request.language)
        return {
            "success": True,
            "email_id": request.email_id,
            "transcription": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status")
async def get_transcription_status():
    """Status endpoint dla gateway"""
    return {
        "service": "transcription",
        "status": "healthy",
        "models_loaded": get_loaded_models(),
        "queue_size": get_queue_size()
    }

@app.get("/health")
async def health_check():
    """Health check dla gateway"""
    return {"status": "healthy"}
```

#### 2.2 Email Processing Service
```python
# python_mixer/email_processing/email_analyzer.py - ADD ENDPOINTS
@app.post("/api/process")
async def process_email(request: EmailProcessingRequest):
    """Endpoint dla gateway - przetwarzanie email"""
    try:
        # Existing email processing logic
        result = await analyze_email(request.email_data)
        return {
            "success": True,
            "email_id": request.email_id,
            "analysis": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status")
async def get_email_processing_status():
    """Status endpoint dla gateway"""
    return {
        "service": "email_processing",
        "status": "healthy",
        "processed_today": get_daily_count(),
        "queue_size": get_queue_size()
    }
```

### **ETAP 3: Configuration Update**

#### 3.1 Python Mixer Config
```python
# python_mixer/config.py - UPDATE
class Config:
    # Gateway settings
    GATEWAY_ENABLED = True
    GATEWAY_URL = "http://localhost:8090"
    
    # Service ports (for direct access if needed)
    ORCHESTRATOR_PORT = 9000
    EMAIL_PROCESSING_PORT = 8082
    TRANSCRIPTION_PORT = 8889
    TELEGRAM_BOT_PORT = 8083
    
    # Gateway routing
    GATEWAY_ROUTES = {
        "transcription": "/api/mixer/transcription",
        "email_processing": "/api/mixer/email", 
        "telegram_bot": "/api/mixer/telegram",
        "orchestrator": "/api/mixer/orchestrator"
    }
```

#### 3.2 Service Discovery
```python
# python_mixer/core/service_discovery.py
class ServiceDiscovery:
    def __init__(self, gateway_url: str):
        self.gateway_url = gateway_url
    
    async def register_service(self, service_name: str, port: int):
        """Zarejestruj usługę w gateway"""
        registration_data = {
            "name": service_name,
            "host": "localhost", 
            "port": port,
            "health_path": "/health",
            "endpoints": self.get_service_endpoints(service_name)
        }
        
        # Send registration to gateway
        async with aiohttp.ClientSession() as session:
            url = f"{self.gateway_url}/api/gateway/services/{service_name}/register"
            await session.post(url, json=registration_data)
    
    def get_service_endpoints(self, service_name: str) -> List[str]:
        """Pobierz endpointy dla usługi"""
        endpoints_map = {
            "transcription": ["/api/transcribe", "/api/status"],
            "email_processing": ["/api/process", "/api/status"],
            "telegram_bot": ["/api/send", "/api/status"],
            "orchestrator": ["/api/execute", "/api/status"]
        }
        return endpoints_map.get(service_name, [])
```

### **ETAP 4: Testing & Validation**

#### 4.1 Integration Tests
```python
# python_mixer/tests/test_gateway_integration.py
import pytest
import asyncio
from core.gateway_client import UnifiedGatewayClient

@pytest.mark.asyncio
async def test_gateway_health():
    """Test połączenia z gateway"""
    async with UnifiedGatewayClient() as client:
        health = await client.get_gateway_health()
        assert health["status"] == "healthy"

@pytest.mark.asyncio 
async def test_transcription_through_gateway():
    """Test transkrypcji przez gateway"""
    async with UnifiedGatewayClient() as client:
        result = await client.send_to_service(
            service="transcription",
            endpoint="process", 
            data={
                "email_id": "test_123",
                "file_path": "/path/to/test.m4a",
                "language": "pl"
            }
        )
        assert result["success"] == True

@pytest.mark.asyncio
async def test_email_processing_through_gateway():
    """Test przetwarzania email przez gateway"""
    async with UnifiedGatewayClient() as client:
        result = await client.send_to_service(
            service="email_processing",
            endpoint="process",
            data={
                "email_id": "test_456", 
                "email_data": {"subject": "Test", "body": "Test content"}
            }
        )
        assert result["success"] == True
```

## 🚀 **IMPLEMENTATION ROADMAP**

### **Tydzień 1: Core Integration**
- [ ] Implementacja UnifiedGatewayClient
- [ ] Aktualizacja Orchestratora
- [ ] Dodanie gateway endpoints do usług

### **Tydzień 2: Service Updates**
- [ ] Transcription service endpoints
- [ ] Email processing endpoints  
- [ ] Telegram bot endpoints
- [ ] Service discovery implementation

### **Tydzień 3: Testing & Optimization**
- [ ] Integration tests
- [ ] Performance testing
- [ ] Error handling validation
- [ ] Documentation update

### **Tydzień 4: Production Deployment**
- [ ] Configuration management
- [ ] Monitoring setup
- [ ] Load testing
- [ ] Go-live preparation

## 📊 **EXPECTED BENEFITS**

### **🎯 Immediate Benefits**
- **Unified Communication** - wszystkie wywołania przez jeden gateway
- **Better Error Handling** - centralized error management
- **Improved Monitoring** - unified metrics and logging

### **📈 Long-term Benefits**  
- **Scalability** - load balancing and service discovery
- **Reliability** - circuit breakers and health monitoring
- **Maintainability** - simplified architecture and debugging

## ✅ **SUCCESS CRITERIA**

- [ ] Wszystkie python_mixer usługi komunikują się przez gateway
- [ ] Zero downtime podczas migracji
- [ ] Improved response times (target: <200ms)
- [ ] 99.9% uptime dla gateway
- [ ] Complete observability i monitoring

---

## 🎉 **READY TO IMPLEMENT!**

Plan integracji jest gotowy do realizacji. Gateway działa perfekcyjnie i czeka na połączenie z python_mixer!

**Next Step: Rozpocznij implementację UnifiedGatewayClient** 🚀
