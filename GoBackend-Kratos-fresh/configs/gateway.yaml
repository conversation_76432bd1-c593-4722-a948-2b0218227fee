# 🌉 UNIFIED COMMUNICATION GATEWAY CONFIGURATION
# Centralizuje komunikację mię<PERSON><PERSON>-Kratos a python_mixer

gateway:
  # Gateway server settings
  port: "8080"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  
  # Python Mixer services configuration
  python_mixer_services:
    orchestrator:
      name: "orchestrator"
      host: "localhost"
      port: 9000
      health_path: "/health"
      priority: 1
      weight: 100
      enabled: true
      endpoints:
        - "/api/execute"
        - "/api/status"
        - "/api/workflow"
    
    email_processing:
      name: "email_processing"
      host: "localhost"
      port: 8082
      health_path: "/health"
      priority: 2
      weight: 80
      enabled: true
      endpoints:
        - "/api/process"
        - "/api/status"
        - "/api/analyze"
    
    transcription:
      name: "transcription"
      host: "localhost"
      port: 8889
      health_path: "/health"
      priority: 3
      weight: 60
      enabled: true
      endpoints:
        - "/api/transcribe"
        - "/api/status"
        - "/api/models"
    
    telegram_bot:
      name: "telegram_bot"
      host: "localhost"
      port: 8083
      health_path: "/health"
      priority: 4
      weight: 40
      enabled: true
      endpoints:
        - "/api/send"
        - "/api/status"
        - "/api/webhook"

  # Redis configuration for caching
  redis:
    addr: "localhost:6379"
    password: ""
    db: 0
    
  # Caching settings
  cache:
    enabled: true
    ttl: "300s"
    key_prefix: "hvac_gateway:"
    
  # Health check settings
  health_check:
    interval: "30s"
    timeout: "10s"
    
  # Circuit breaker configuration per service
  circuit_breaker_config:
    orchestrator:
      max_requests: 5
      interval: "60s"
      timeout: "30s"
      failure_ratio: 0.6
      min_requests: 3
      
    email_processing:
      max_requests: 10
      interval: "60s"
      timeout: "20s"
      failure_ratio: 0.7
      min_requests: 5
      
    transcription:
      max_requests: 3
      interval: "120s"
      timeout: "60s"
      failure_ratio: 0.5
      min_requests: 2
      
    telegram_bot:
      max_requests: 15
      interval: "60s"
      timeout: "15s"
      failure_ratio: 0.8
      min_requests: 5

  # Rate limiting configuration per service
  rate_limit_config:
    orchestrator:
      requests_per_second: 10
      burst_size: 20
      window_size: "60s"
      
    email_processing:
      requests_per_second: 20
      burst_size: 40
      window_size: "60s"
      
    transcription:
      requests_per_second: 5
      burst_size: 10
      window_size: "60s"
      
    telegram_bot:
      requests_per_second: 30
      burst_size: 60
      window_size: "60s"

# Monitoring and observability
monitoring:
  metrics:
    enabled: true
    endpoint: "/api/gateway/metrics"
    collection_interval: "30s"
    
  health:
    enabled: true
    endpoint: "/api/gateway/health"
    
  tracing:
    enabled: true
    service_name: "unified-gateway"
    
  logging:
    level: "info"
    format: "json"
    output: "stdout"

# Security settings
security:
  cors:
    enabled: true
    allowed_origins:
      - "http://localhost:3000"
      - "http://localhost:8080"
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-Requested-With"]
    
  rate_limiting:
    global_enabled: true
    global_requests_per_second: 100
    global_burst_size: 200
    
  authentication:
    enabled: false  # Disable for development
    jwt_secret: "your-jwt-secret-here"
    token_expiry: "24h"

# WebSocket configuration
websocket:
  enabled: true
  endpoint: "/ws/gateway"
  ping_interval: "30s"
  pong_wait: "60s"
  write_wait: "10s"
  max_message_size: 512

# Load balancing
load_balancing:
  strategy: "round_robin"  # round_robin, least_connections, weighted
  health_check_required: true
  failover_enabled: true
  
# Service discovery
service_discovery:
  enabled: true
  discovery_interval: "60s"
  auto_register: true
  consul:
    enabled: false
    address: "localhost:8500"
  
# Performance tuning
performance:
  max_idle_conns: 100
  max_idle_conns_per_host: 10
  idle_conn_timeout: "90s"
  request_timeout: "30s"
  keep_alive: "30s"
  
  # Connection pooling
  connection_pool:
    max_connections: 200
    max_idle_connections: 50
    connection_timeout: "30s"
    
  # Buffer sizes
  buffers:
    read_buffer_size: 4096
    write_buffer_size: 4096
    
# Development settings
development:
  debug_mode: true
  hot_reload: true
  detailed_logging: true
  mock_services: false

# Production settings
production:
  debug_mode: false
  hot_reload: false
  detailed_logging: false
  enable_profiling: true
  graceful_shutdown_timeout: "30s"
