package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"gobackend-hvac-kratos/internal/foundations/web"
)

// 🚀 GoSpine - Unified HVAC CRM Backend
// Production-ready, streamlined backend serving as single source of truth

const (
	ServiceName    = "gospine-hvac-crm"
	ServiceVersion = "1.0.0"
	DefaultPort    = "8080"
)

// Application holds all service dependencies
type Application struct {
	// Core infrastructure
	logger *zap.Logger
	db     *gorm.DB
	server *web.GinServer

	// Context management
	ctx    context.Context
	cancel context.CancelFunc
}

func main() {
	printBanner()

	app, err := NewApplication()
	if err != nil {
		log.Fatalf("❌ Failed to initialize application: %v", err)
	}

	if err := app.Start(); err != nil {
		log.Fatalf("❌ Failed to start application: %v", err)
	}

	app.WaitForShutdown()
}

// printBanner displays the startup banner
func printBanner() {
	fmt.Println(`
╔══════════════════════════════════════════════════════════════════════════════╗
║                           🚀 GoSpine HVAC CRM                               ║
║                     Production-Ready Backend System                          ║
║                                                                              ║
║  🎤 Enhanced Transcription  📧 Email Intelligence  🔧 Equipment Management  ║
║  📊 Real-time Analytics     🔐 Secure Configuration  🌐 HTTP/gRPC APIs      ║
╚══════════════════════════════════════════════════════════════════════════════╝
	`)
}

// NewApplication creates and initializes the application
func NewApplication() (*Application, error) {
	ctx, cancel := context.WithCancel(context.Background())

	app := &Application{
		ctx:    ctx,
		cancel: cancel,
	}

	// Initialize components in order
	if err := app.initLogger(); err != nil {
		return nil, fmt.Errorf("failed to init logger: %w", err)
	}

	if err := app.initDatabase(); err != nil {
		return nil, fmt.Errorf("failed to init database: %w", err)
	}

	if err := app.initServer(); err != nil {
		return nil, fmt.Errorf("failed to init server: %w", err)
	}

	return app, nil
}

// initLogger initializes the logging system
func (app *Application) initLogger() error {
	logger, err := zap.NewProduction()
	if err != nil {
		return fmt.Errorf("failed to create logger: %w", err)
	}

	app.logger = logger
	app.logger.Info("✅ Logger initialized")
	return nil
}

// initDatabase initializes database connection
func (app *Application) initDatabase() error {
	// Use environment variables or defaults
	host := getEnvOrDefault("POSTGRESQL_HOST", "**************")
	port := getEnvOrDefault("POSTGRESQL_PORT", "5432")
	user := getEnvOrDefault("POSTGRESQL_USER", "postgres")
	password := getEnvOrDefault("POSTGRESQL_PASSWORD", "Blaeritipol1")
	dbname := getEnvOrDefault("POSTGRESQL_DATABASE", "hvac_crm")

	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	app.db = db
	app.logger.Info("✅ Database connection established")
	return nil
}

// initServer initializes the web server
func (app *Application) initServer() error {
	// Create enhanced web configuration
	config := &web.WebConfig{
		Port:            getEnvOrDefault("HTTP_PORT", DefaultPort),
		Mode:            gin.ReleaseMode,
		ReadTimeout:     30 * time.Second,
		WriteTimeout:    30 * time.Second,
		IdleTimeout:     60 * time.Second,
		ShutdownTimeout: 10 * time.Second,
		EnableCORS:      true,
		EnableMetrics:   true,
		EnableRecovery:  true,
		TrustedProxies:  []string{"127.0.0.1"},
	}

	// Create Gin server with enhanced features
	app.server = web.NewGinServer(config, app.logger)

	// Setup routes
	app.setupRoutes()

	app.logger.Info("✅ Server initialized",
		zap.String("port", config.Port),
		zap.String("mode", config.Mode))
	return nil
}

// setupRoutes configures HTTP routes
func (app *Application) setupRoutes() {
	engine := app.server.Engine()

	// Health check endpoints
	engine.GET("/health", app.handleHealth)
	engine.GET("/health/detailed", app.handleDetailedHealth)

	// API v1 routes
	v1 := engine.Group("/api/v1")
	{
		// Core CRM endpoints
		crm := v1.Group("/crm")
		{
			crm.GET("/customers", app.handleCustomers)
			crm.GET("/leads", app.handleLeads)
			crm.GET("/equipment", app.handleEquipment)
			crm.GET("/service-orders", app.handleServiceOrders)
		}

		// Transcription endpoints
		transcription := v1.Group("/transcription")
		{
			transcription.POST("/submit", app.handleSubmitTranscription)
			transcription.GET("/status/:jobId", app.handleTranscriptionStatus)
			transcription.GET("/metrics", app.handleTranscriptionMetrics)
		}

		// Email intelligence endpoints
		email := v1.Group("/email")
		{
			email.GET("/intelligence", app.handleEmailIntelligence)
			email.POST("/process", app.handleEmailProcess)
		}
	}

	// Main dashboard
	engine.GET("/", app.handleDashboard)
	engine.GET("/dashboard", app.handleDashboard)

	// Static files
	engine.Static("/static", "./web/static")
}

// Start starts the application
func (app *Application) Start() error {
	app.logger.Info("🚀 Starting GoSpine HVAC CRM System...")

	// Start server in goroutine
	go func() {
		app.printServiceInfo()
		if err := app.server.Start(); err != nil {
			app.logger.Error("❌ Server failed to start", zap.Error(err))
		}
	}()

	app.logger.Info("✅ GoSpine system started successfully")
	return nil
}

// printServiceInfo displays service information
func (app *Application) printServiceInfo() {
	port := getEnvOrDefault("HTTP_PORT", DefaultPort)

	fmt.Printf(`
🎉 ======================================
🚀 GoSpine HVAC CRM System Ready!
======================================
📊 Main Dashboard:     http://localhost:%s/dashboard
🔍 Health Check:       http://localhost:%s/health
👥 CRM API:           http://localhost:%s/api/v1/crm
🎤 Transcription API:  http://localhost:%s/api/v1/transcription
📧 Email Intelligence: http://localhost:%s/api/v1/email
======================================

`, port, port, port, port, port)
}

// WaitForShutdown waits for shutdown signal and gracefully stops the application
func (app *Application) WaitForShutdown() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	app.logger.Info("🎯 GoSpine system is running! Press Ctrl+C to stop...")
	<-sigChan

	app.logger.Info("🛑 Shutdown signal received, stopping services...")
	app.Shutdown()
}

// Shutdown gracefully stops all application components
func (app *Application) Shutdown() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Stop server
	if app.server != nil {
		if err := app.server.Stop(ctx); err != nil {
			app.logger.Error("❌ Error stopping server", zap.Error(err))
		} else {
			app.logger.Info("✅ Server stopped")
		}
	}

	// Close database
	if app.db != nil {
		if sqlDB, err := app.db.DB(); err == nil {
			sqlDB.Close()
			app.logger.Info("✅ Database connection closed")
		}
	}

	// Cancel context
	app.cancel()

	app.logger.Info("✅ GoSpine system shutdown complete")
}

// Helper function to get environment variable with default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// HTTP Handlers

// handleHealth provides basic health check
func (app *Application) handleHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   ServiceName,
		"version":   ServiceVersion,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleDetailedHealth provides detailed health information
func (app *Application) handleDetailedHealth(c *gin.Context) {
	checks := make(map[string]interface{})
	overallHealthy := true

	// Database health check
	if app.db != nil {
		if sqlDB, err := app.db.DB(); err == nil {
			if err := sqlDB.PingContext(app.ctx); err == nil {
				checks["database"] = gin.H{"healthy": true, "error": ""}
			} else {
				checks["database"] = gin.H{"healthy": false, "error": err.Error()}
				overallHealthy = false
			}
		} else {
			checks["database"] = gin.H{"healthy": false, "error": "database not available"}
			overallHealthy = false
		}
	} else {
		checks["database"] = gin.H{"healthy": false, "error": "database not initialized"}
		overallHealthy = false
	}

	status := http.StatusOK
	if !overallHealthy {
		status = http.StatusServiceUnavailable
	}

	c.JSON(status, gin.H{
		"status": func() string {
			if overallHealthy {
				return "healthy"
			}
			return "unhealthy"
		}(),
		"service":   ServiceName,
		"version":   ServiceVersion,
		"checks":    checks,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleDashboard serves the main dashboard
func (app *Application) handleDashboard(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"title":   "GoSpine HVAC CRM Dashboard",
		"version": ServiceVersion,
		"message": "Welcome to GoSpine HVAC CRM System",
		"endpoints": gin.H{
			"health":        "/health",
			"customers":     "/api/v1/crm/customers",
			"leads":         "/api/v1/crm/leads",
			"equipment":     "/api/v1/crm/equipment",
			"transcription": "/api/v1/transcription",
			"email":         "/api/v1/email",
		},
	})
}

// CRM Handlers
func (app *Application) handleCustomers(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Customer management endpoint",
		"status":  "coming soon",
		"data":    []interface{}{},
	})
}

func (app *Application) handleLeads(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Lead management endpoint",
		"status":  "coming soon",
		"data":    []interface{}{},
	})
}

func (app *Application) handleEquipment(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Equipment management endpoint",
		"status":  "coming soon",
		"data":    []interface{}{},
	})
}

func (app *Application) handleServiceOrders(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Service orders endpoint",
		"status":  "coming soon",
		"data":    []interface{}{},
	})
}

// Transcription Handlers
func (app *Application) handleSubmitTranscription(c *gin.Context) {
	var req struct {
		EmailSource string            `json:"email_source"`
		FileName    string            `json:"file_name"`
		AudioData   []byte            `json:"audio_data"`
		Metadata    map[string]string `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: Integrate with transcription orchestrator
	c.JSON(http.StatusCreated, gin.H{
		"job_id":     fmt.Sprintf("job_%d", time.Now().Unix()),
		"status":     "submitted",
		"message":    "Transcription job submitted successfully",
		"created_at": time.Now(),
	})
}

func (app *Application) handleTranscriptionStatus(c *gin.Context) {
	jobID := c.Param("jobId")

	// TODO: Get actual job status
	c.JSON(http.StatusOK, gin.H{
		"job_id":  jobID,
		"status":  "processing",
		"message": "Job status endpoint",
	})
}

func (app *Application) handleTranscriptionMetrics(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"total_jobs":     0,
		"completed_jobs": 0,
		"failed_jobs":    0,
		"active_jobs":    0,
		"last_updated":   time.Now(),
	})
}

// Email Intelligence Handlers
func (app *Application) handleEmailIntelligence(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Email intelligence endpoint",
		"status":  "coming soon",
		"data":    []interface{}{},
	})
}

func (app *Application) handleEmailProcess(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Email processing endpoint",
		"status":  "coming soon",
	})
}
