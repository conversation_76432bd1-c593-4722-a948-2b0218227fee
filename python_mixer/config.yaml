# Centralized Configuration for python_mixer

email:
  dolores:
    host: imap.gmail.com
    port: 993
    username: dolore<PERSON>@koldbringers.pl
    password: YOUR_DOLORES_EMAIL_PASSWORD # IMPORTANT: Use environment variables or a secrets manager in production!
    use_ssl: true
  grz<PERSON>rz:
    host: imap.gmail.com
    port: 993
    username: g<PERSON><PERSON><PERSON>@koldbringers.pl
    password: YOUR_GRZEGORZ_EMAIL_PASSWORD # IMPORTANT: Use environment variables or a secrets manager in production!
    use_ssl: true

services:
  nemo_stt: http://localhost:8889
  transcription_orchestrator: http://localhost:9000
  gemma_integration: http://*************:1234 # LM Studio
  postgres: ********************************************************/hvac_crm

test_settings:
  target_processing_time: 30.0 # seconds
  target_accuracy: 0.95 # 95%
  target_confidence: 0.80 # 80%
  max_test_files: 10
  test_timeout: 300 # 5 minutes per test