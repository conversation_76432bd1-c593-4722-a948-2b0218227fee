#!/usr/bin/env python3
"""
🎯 DOLORES ARCHIVE PIPELINE TEST
===============================
Test the database integration pipeline with real Dolores archive data
"""

import asyncio
import json
import os
import re
from datetime import datetime, timezone
from pathlib import Path
from loguru import logger

# Import pipeline components
from demo_database_pipeline import MockETLPipeline, MockDatabaseConfig

class DoloresArchiveProcessor:
    """Process real Dolores archive data through the pipeline"""
    
    def __init__(self):
        self.archive_path = Path("dolores_email_archive")
        self.metadata_path = self.archive_path / "metadata"
        self.attachments_path = self.archive_path / "attachments"
        self.processed_emails = []
        
    async def process_archive_with_pipeline(self, limit: int = 10):
        """Process real archive data through the database pipeline"""
        logger.info("🎯 PROCESSING REAL DOLORES ARCHIVE DATA")
        logger.info("=" * 60)
        
        # Initialize pipeline
        config = MockDatabaseConfig()
        pipeline = MockETLPipeline(config)
        
        try:
            await pipeline.initialize()
            
            # Load real email metadata
            real_emails = await self._load_real_emails(limit)
            logger.info(f"📊 Loaded {len(real_emails)} real emails from archive")
            
            processed_count = 0
            
            for email_data in real_emails:
                try:
                    # Stage 1: Ingest into staging
                    email_id = await pipeline.staging_layer.ingest_raw_email(email_data)
                    
                    # Stage 2: Process with real data extraction
                    processed_record = await self._process_real_email(email_id, email_data)
                    
                    # Stage 3: Store in operational database
                    record_id = await pipeline.operational_layer.store_processed_email(processed_record)
                    
                    # Mark as processed
                    await pipeline.staging_layer.mark_email_processed(email_id)
                    
                    self.processed_emails.append({
                        "email_id": email_id,
                        "record_id": record_id,
                        "customer_name": processed_record.customer_data.customer_name,
                        "phone_numbers": processed_record.customer_data.phone_numbers,
                        "urgency_level": processed_record.hvac_context.urgency_level,
                        "business_value_score": processed_record.business_value_score,
                        "has_audio": processed_record.audio_metadata is not None
                    })
                    
                    processed_count += 1
                    logger.info(f"📈 Processed real email {processed_count}/{len(real_emails)}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to process email: {e}")
                    continue
                    
            # Generate comprehensive report
            await self._generate_real_data_report(processed_count)
            
        except Exception as e:
            logger.error(f"❌ Archive processing failed: {e}")
            raise
        finally:
            await pipeline.cleanup()
            
    async def _load_real_emails(self, limit: int) -> list:
        """Load real email metadata from archive"""
        emails = []
        
        if not self.metadata_path.exists():
            logger.warning(f"⚠️ Archive path not found: {self.metadata_path}")
            return emails
            
        metadata_files = list(self.metadata_path.glob("*.json"))[:limit]
        
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    email_data = json.load(f)
                    
                # Enhance with attachment information
                email_data = await self._enhance_with_attachments(email_data)
                emails.append(email_data)
                
            except Exception as e:
                logger.error(f"❌ Failed to load {metadata_file}: {e}")
                
        return emails
        
    async def _enhance_with_attachments(self, email_data: dict) -> dict:
        """Enhance email data with attachment information"""
        
        # Check for M4A attachments
        email_id = email_data.get('email_id', '')
        m4a_files = []
        
        if self.attachments_path.exists():
            # Look for M4A files matching this email
            pattern = f"email_{email_id}_*.m4a"
            m4a_matches = list(self.attachments_path.glob(pattern))
            m4a_files = [str(f) for f in m4a_matches]
            
        email_data['m4a_attachments'] = m4a_files
        email_data['has_attachments'] = len(m4a_files) > 0
        email_data['attachment_count'] = len(m4a_files)
        
        return email_data
        
    async def _process_real_email(self, email_id: str, email_data: dict):
        """Process real email data with enhanced extraction"""
        from database_integration_pipeline import (
            EmailMetadata, CustomerData, AudioMetadata, HVACContext, 
            ProcessedEmailRecord, DataExtractor
        )
        
        extractor = DataExtractor()
        
        # Extract email metadata
        email_metadata = EmailMetadata(
            email_id=email_id,
            message_id=email_data.get('message_id', ''),
            subject=email_data.get('subject', ''),
            sender=email_data.get('from', ''),
            recipient=email_data.get('to', ''),
            received_timestamp=datetime.now(timezone.utc),
            processing_timestamp=datetime.now(timezone.utc),
            raw_content=email_data.get('content', ''),
            has_attachments=email_data.get('has_attachments', False),
            attachment_count=email_data.get('attachment_count', 0),
            m4a_attachments=email_data.get('m4a_attachments', [])
        )
        
        # Extract customer data using real extraction logic
        customer_data = extractor.extract_customer_data(email_metadata)
        
        # Process audio if available
        audio_metadata = None
        if email_metadata.m4a_attachments:
            audio_metadata = await self._process_real_audio(email_metadata.m4a_attachments[0])
            
        # Extract HVAC context
        hvac_context = extractor.extract_hvac_context(email_metadata, audio_metadata)
        
        # Calculate business value
        business_value_score = self._calculate_real_business_value(
            customer_data, hvac_context, audio_metadata
        )
        
        # Create data lineage
        data_lineage = {
            "processing_timestamp": datetime.now(timezone.utc),
            "extraction_methods": ["real_phone_regex", "real_name_pattern", "real_hvac_keywords"],
            "confidence_scores": {
                "customer_data": customer_data.confidence_score,
                "transcription": audio_metadata.transcription_confidence if audio_metadata else 0.0
            },
            "processing_stage": "real_data_processing_complete"
        }
        
        return ProcessedEmailRecord(
            email_metadata=email_metadata,
            customer_data=customer_data,
            audio_metadata=audio_metadata,
            hvac_context=hvac_context,
            data_lineage=data_lineage,
            business_value_score=business_value_score
        )
        
    async def _process_real_audio(self, m4a_file: str):
        """Process real M4A audio file"""
        from database_integration_pipeline import AudioMetadata
        
        # Get real file information
        file_size = 0
        if os.path.exists(m4a_file):
            file_size = os.path.getsize(m4a_file)
            
        # Mock transcription for now (in production, use real STT)
        mock_transcriptions = [
            "Dzień dobry, dzwonię w sprawie awarii klimatyzacji Daikin.",
            "Witam, potrzebuję serwisu klimatyzacji LG w biurze.",
            "Proszę o wycenę instalacji nowej klimatyzacji.",
            "Klimatyzacja nie chłodzi, potrzebuję pilnej naprawy.",
            "Czy możecie przyjechać na przegląd klimatyzacji?"
        ]
        
        import random
        transcription = random.choice(mock_transcriptions)
        
        # Detect keywords in transcription
        keywords = []
        hvac_keywords = ['klimatyzacja', 'serwis', 'awaria', 'daikin', 'lg', 'naprawa', 'instalacja']
        for keyword in hvac_keywords:
            if keyword in transcription.lower():
                keywords.append(keyword)
                
        return AudioMetadata(
            filename=os.path.basename(m4a_file),
            file_size=file_size,
            duration_seconds=file_size / 8000 if file_size > 0 else 120.0,  # Estimate duration
            transcription_text=transcription,
            transcription_confidence=0.85,
            keywords_detected=keywords,
            processing_time=2.5
        )
        
    def _calculate_real_business_value(self, customer_data, hvac_context, audio_metadata) -> float:
        """Calculate business value for real data"""
        score = 0.0
        
        # Customer data value
        if customer_data.phone_numbers:
            score += 0.3
        if customer_data.customer_name:
            score += 0.2
        if customer_data.address:
            score += 0.1
            
        # HVAC context value
        if hvac_context.urgency_level in ["high", "critical"]:
            score += 0.2
        if hvac_context.equipment_brands:
            score += 0.1
        if "breakdown" in hvac_context.service_types:
            score += 0.1
            
        # Audio transcription value
        if audio_metadata and audio_metadata.transcription_confidence > 0.8:
            score += 0.1
            
        return min(1.0, score)
        
    async def _generate_real_data_report(self, processed_count: int):
        """Generate comprehensive report for real data processing"""
        
        # Analyze processed emails
        phone_extraction_rate = sum(1 for email in self.processed_emails if email['phone_numbers']) / len(self.processed_emails) * 100 if self.processed_emails else 0
        name_extraction_rate = sum(1 for email in self.processed_emails if email['customer_name']) / len(self.processed_emails) * 100 if self.processed_emails else 0
        audio_processing_rate = sum(1 for email in self.processed_emails if email['has_audio']) / len(self.processed_emails) * 100 if self.processed_emails else 0
        
        high_value_emails = sum(1 for email in self.processed_emails if email['business_value_score'] > 0.7)
        urgent_requests = sum(1 for email in self.processed_emails if email['urgency_level'] in ['high', 'critical'])
        
        report = {
            "real_data_processing": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "processed_emails": processed_count,
                "archive_source": "dolores_email_archive",
                "processing_mode": "real_data_extraction"
            },
            "extraction_performance": {
                "phone_extraction_rate": f"{phone_extraction_rate:.1f}%",
                "name_extraction_rate": f"{name_extraction_rate:.1f}%",
                "audio_processing_rate": f"{audio_processing_rate:.1f}%"
            },
            "business_metrics": {
                "high_value_emails": high_value_emails,
                "urgent_requests": urgent_requests,
                "average_business_value": sum(email['business_value_score'] for email in self.processed_emails) / len(self.processed_emails) if self.processed_emails else 0
            },
            "processed_records": self.processed_emails[:5]  # First 5 records as sample
        }
        
        # Save report
        report_file = f"real_data_pipeline_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
        logger.info(f"📋 Real data report saved: {report_file}")
        
        # Display summary
        logger.info("=" * 60)
        logger.info("🎉 REAL DATA PROCESSING COMPLETED!")
        logger.info(f"📊 Processed Emails: {processed_count}")
        logger.info(f"📞 Phone Extraction Rate: {phone_extraction_rate:.1f}%")
        logger.info(f"👤 Name Extraction Rate: {name_extraction_rate:.1f}%")
        logger.info(f"🎤 Audio Processing Rate: {audio_processing_rate:.1f}%")
        logger.info(f"💎 High Value Emails: {high_value_emails}")
        logger.info(f"🚨 Urgent Requests: {urgent_requests}")
        logger.info("=" * 60)

async def main():
    """Main test function"""
    processor = DoloresArchiveProcessor()
    
    # Process 10 real emails from the archive
    await processor.process_archive_with_pipeline(limit=10)

if __name__ == "__main__":
    asyncio.run(main())
