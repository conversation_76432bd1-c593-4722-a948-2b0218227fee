2025-05-30 23:34:17.599 | INFO     | __main__:main:331 - 🎯 DOLORES DATABASE INTEGRATION PIPELINE - DEMO MODE
2025-05-30 23:34:17.599 | INFO     | __main__:main:332 - ============================================================
2025-05-30 23:34:17.599 | INFO     | __main__:main:333 - 🧪 Running with mock databases and sample data
2025-05-30 23:34:17.599 | INFO     | __main__:main:334 - ============================================================
2025-05-30 23:34:17.599 | INFO     | __main__:initialize:119 - 🚀 Initializing Mock ETL Pipeline...
2025-05-30 23:34:17.599 | INFO     | __main__:connect_all:45 - 🔗 Connecting to mock databases...
2025-05-30 23:34:20.346 | INFO     | __main__:connect_all:48 - ✅ Mock database connections established
2025-05-30 23:34:20.346 | INFO     | __main__:initialize_schema:89 - 🏗️ Initializing mock PostgreSQL schema...
2025-05-30 23:34:20.847 | INFO     | __main__:initialize_schema:91 - ✅ Mock schema initialized
2025-05-30 23:34:20.847 | INFO     | __main__:initialize:122 - ✅ Mock ETL Pipeline initialized
2025-05-30 23:34:20.847 | INFO     | __main__:process_sample_emails:126 - 📧 Processing sample Dolores emails...
2025-05-30 23:34:20.847 | INFO     | __main__:ingest_raw_email:71 - ✅ Mock email ingested: mock_1
2025-05-30 23:34:20.848 | INFO     | __main__:store_processed_email:105 - ✅ Mock email stored with record_id: 1
2025-05-30 23:34:20.848 | INFO     | __main__:mark_email_processed:78 - ✅ Mock email marked as processed: mock_1
2025-05-30 23:34:20.848 | INFO     | __main__:process_sample_emails:176 - 📈 Processed email 1/3
2025-05-30 23:34:20.848 | INFO     | __main__:ingest_raw_email:71 - ✅ Mock email ingested: mock_2
2025-05-30 23:34:20.849 | INFO     | __main__:store_processed_email:105 - ✅ Mock email stored with record_id: 2
2025-05-30 23:34:20.849 | INFO     | __main__:mark_email_processed:78 - ✅ Mock email marked as processed: mock_2
2025-05-30 23:34:20.849 | INFO     | __main__:process_sample_emails:176 - 📈 Processed email 2/3
2025-05-30 23:34:20.849 | INFO     | __main__:ingest_raw_email:71 - ✅ Mock email ingested: mock_3
2025-05-30 23:34:20.849 | INFO     | __main__:store_processed_email:105 - ✅ Mock email stored with record_id: 3
2025-05-30 23:34:20.849 | INFO     | __main__:mark_email_processed:78 - ✅ Mock email marked as processed: mock_3
2025-05-30 23:34:20.849 | INFO     | __main__:process_sample_emails:176 - 📈 Processed email 3/3
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:310 - 📋 Demo report saved: demo_pipeline_report_20250530_233420.json
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:313 - ============================================================
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:314 - 🎉 DEMO PIPELINE EXECUTION COMPLETED!
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:315 - 📊 Processed Emails: 3
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:316 - 👥 Customers Identified: 3
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:317 - 📞 Phone Numbers Extracted: 3
2025-05-30 23:34:20.850 | INFO     | __main__:_generate_demo_report:318 - 🔧 Service Requests Created: 3
2025-05-30 23:34:20.851 | INFO     | __main__:_generate_demo_report:319 - 🎤 Audio Transcriptions: 2
2025-05-30 23:34:20.851 | INFO     | __main__:_generate_demo_report:320 - ============================================================
2025-05-30 23:34:20.851 | INFO     | __main__:main:347 - 🎉 Demo completed successfully!
2025-05-30 23:34:20.851 | INFO     | __main__:close_all:52 - 🧹 Closing mock database connections
2025-05-30 23:34:20.851 | INFO     | __main__:cleanup:327 - 🧹 Demo pipeline cleanup completed
