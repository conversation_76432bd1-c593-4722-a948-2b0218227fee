2025-05-30 23:33:55.614 | INFO     | __main__:main:331 - 🎯 DOLORES DATABASE INTEGRATION PIPELINE - DEMO MODE
2025-05-30 23:33:55.614 | INFO     | __main__:main:332 - ============================================================
2025-05-30 23:33:55.614 | INFO     | __main__:main:333 - 🧪 Running with mock databases and sample data
2025-05-30 23:33:55.615 | INFO     | __main__:main:334 - ============================================================
2025-05-30 23:33:55.615 | INFO     | __main__:initialize:119 - 🚀 Initializing Mock ETL Pipeline...
2025-05-30 23:33:55.615 | INFO     | __main__:connect_all:45 - 🔗 Connecting to mock databases...
2025-05-30 23:33:56.616 | INFO     | __main__:connect_all:48 - ✅ Mock database connections established
2025-05-30 23:33:56.616 | INFO     | __main__:initialize_schema:89 - 🏗️ Initializing mock PostgreSQL schema...
2025-05-30 23:33:57.117 | INFO     | __main__:initialize_schema:91 - ✅ Mock schema initialized
2025-05-30 23:33:57.117 | INFO     | __main__:initialize:122 - ✅ Mock ETL Pipeline initialized
2025-05-30 23:33:57.117 | INFO     | __main__:process_sample_emails:126 - 📧 Processing sample Dolores emails...
2025-05-30 23:33:57.117 | INFO     | __main__:ingest_raw_email:71 - ✅ Mock email ingested: mock_1
2025-05-30 23:33:57.119 | INFO     | __main__:store_processed_email:105 - ✅ Mock email stored with record_id: 1
2025-05-30 23:33:57.119 | INFO     | __main__:mark_email_processed:78 - ✅ Mock email marked as processed: mock_1
2025-05-30 23:33:57.119 | INFO     | __main__:process_sample_emails:176 - 📈 Processed email 1/3
2025-05-30 23:33:57.119 | INFO     | __main__:ingest_raw_email:71 - ✅ Mock email ingested: mock_2
2025-05-30 23:33:57.119 | INFO     | __main__:store_processed_email:105 - ✅ Mock email stored with record_id: 2
2025-05-30 23:33:57.119 | INFO     | __main__:mark_email_processed:78 - ✅ Mock email marked as processed: mock_2
2025-05-30 23:33:57.119 | INFO     | __main__:process_sample_emails:176 - 📈 Processed email 2/3
2025-05-30 23:33:57.119 | INFO     | __main__:ingest_raw_email:71 - ✅ Mock email ingested: mock_3
2025-05-30 23:33:57.119 | INFO     | __main__:store_processed_email:105 - ✅ Mock email stored with record_id: 3
2025-05-30 23:33:57.119 | INFO     | __main__:mark_email_processed:78 - ✅ Mock email marked as processed: mock_3
2025-05-30 23:33:57.119 | INFO     | __main__:process_sample_emails:176 - 📈 Processed email 3/3
2025-05-30 23:33:57.120 | ERROR    | __main__:main:350 - ❌ Demo failed: Object of type datetime is not JSON serializable
2025-05-30 23:33:57.120 | INFO     | __main__:close_all:52 - 🧹 Closing mock database connections
2025-05-30 23:33:57.120 | INFO     | __main__:cleanup:327 - 🧹 Demo pipeline cleanup completed
