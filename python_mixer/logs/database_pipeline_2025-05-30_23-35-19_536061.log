2025-05-30 23:35:19.542 | INFO     | __main__:process_archive_with_pipeline:30 - 🎯 PROCESSING REAL DOLORES ARCHIVE DATA
2025-05-30 23:35:19.542 | INFO     | __main__:process_archive_with_pipeline:31 - ============================================================
2025-05-30 23:35:19.542 | INFO     | demo_database_pipeline:initialize:119 - 🚀 Initializing Mock ETL Pipeline...
2025-05-30 23:35:19.542 | INFO     | demo_database_pipeline:connect_all:45 - 🔗 Connecting to mock databases...
2025-05-30 23:35:20.543 | INFO     | demo_database_pipeline:connect_all:48 - ✅ Mock database connections established
2025-05-30 23:35:20.544 | INFO     | demo_database_pipeline:initialize_schema:89 - 🏗️ Initializing mock PostgreSQL schema...
2025-05-30 23:35:21.044 | INFO     | demo_database_pipeline:initialize_schema:91 - ✅ Mock schema initialized
2025-05-30 23:35:21.044 | INFO     | demo_database_pipeline:initialize:122 - ✅ Mock ETL Pipeline initialized
2025-05-30 23:35:21.089 | INFO     | __main__:process_archive_with_pipeline:42 - 📊 Loaded 10 real emails from archive
2025-05-30 23:35:21.089 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_1
2025-05-30 23:35:21.093 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 1
2025-05-30 23:35:21.094 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_1
2025-05-30 23:35:21.094 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 1/10
2025-05-30 23:35:21.094 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_2
2025-05-30 23:35:21.094 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 2
2025-05-30 23:35:21.095 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_2
2025-05-30 23:35:21.095 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 2/10
2025-05-30 23:35:21.095 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_3
2025-05-30 23:35:21.095 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 3
2025-05-30 23:35:21.095 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_3
2025-05-30 23:35:21.096 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 3/10
2025-05-30 23:35:21.096 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_4
2025-05-30 23:35:21.096 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 4
2025-05-30 23:35:21.096 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_4
2025-05-30 23:35:21.096 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 4/10
2025-05-30 23:35:21.096 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_5
2025-05-30 23:35:21.097 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 5
2025-05-30 23:35:21.097 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_5
2025-05-30 23:35:21.097 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 5/10
2025-05-30 23:35:21.097 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_6
2025-05-30 23:35:21.097 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 6
2025-05-30 23:35:21.097 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_6
2025-05-30 23:35:21.098 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 6/10
2025-05-30 23:35:21.098 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_7
2025-05-30 23:35:21.098 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 7
2025-05-30 23:35:21.098 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_7
2025-05-30 23:35:21.098 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 7/10
2025-05-30 23:35:21.098 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_8
2025-05-30 23:35:21.098 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 8
2025-05-30 23:35:21.099 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_8
2025-05-30 23:35:21.099 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 8/10
2025-05-30 23:35:21.099 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_9
2025-05-30 23:35:21.099 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 9
2025-05-30 23:35:21.099 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_9
2025-05-30 23:35:21.099 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 9/10
2025-05-30 23:35:21.099 | INFO     | demo_database_pipeline:ingest_raw_email:71 - ✅ Mock email ingested: mock_10
2025-05-30 23:35:21.100 | INFO     | demo_database_pipeline:store_processed_email:105 - ✅ Mock email stored with record_id: 10
2025-05-30 23:35:21.100 | INFO     | demo_database_pipeline:mark_email_processed:78 - ✅ Mock email marked as processed: mock_10
2025-05-30 23:35:21.100 | INFO     | __main__:process_archive_with_pipeline:71 - 📈 Processed real email 10/10
2025-05-30 23:35:21.100 | INFO     | __main__:_generate_real_data_report:289 - 📋 Real data report saved: real_data_pipeline_report_20250530_233521.json
2025-05-30 23:35:21.100 | INFO     | __main__:_generate_real_data_report:292 - ============================================================
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:293 - 🎉 REAL DATA PROCESSING COMPLETED!
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:294 - 📊 Processed Emails: 10
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:295 - 📞 Phone Extraction Rate: 0.0%
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:296 - 👤 Name Extraction Rate: 0.0%
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:297 - 🎤 Audio Processing Rate: 0.0%
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:298 - 💎 High Value Emails: 0
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:299 - 🚨 Urgent Requests: 0
2025-05-30 23:35:21.101 | INFO     | __main__:_generate_real_data_report:300 - ============================================================
2025-05-30 23:35:21.101 | INFO     | demo_database_pipeline:close_all:52 - 🧹 Closing mock database connections
2025-05-30 23:35:21.102 | INFO     | demo_database_pipeline:cleanup:327 - 🧹 Demo pipeline cleanup completed
