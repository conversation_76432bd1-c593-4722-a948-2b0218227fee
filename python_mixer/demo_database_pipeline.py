#!/usr/bin/env python3
"""
🎯 DOLORES DATABASE PIPELINE DEMO
================================
Demo script to test the database integration pipeline with mock data
"""

import asyncio
import json
import os
from datetime import datetime, timezone
from pathlib import Path
from loguru import logger

# Import our pipeline components
from database_integration_pipeline import (
    DatabaseConfig, ETLPipeline, EmailMetadata, CustomerData, 
    AudioMetadata, HVACContext, ProcessedEmailRecord
)

class MockDatabaseConfig(DatabaseConfig):
    """Mock database configuration for testing"""
    
    def __init__(self):
        super().__init__()
        # Use SQLite for demo instead of PostgreSQL
        self.POSTGRES_HOST = "localhost"
        self.POSTGRES_DATABASE = "demo_hvac_crm"
        self.POSTGRES_USER = "demo_user"
        self.POSTGRES_PASSWORD = "demo_password"
        
        # Mock MongoDB/Redis (we'll simulate these)
        self.MONGODB_URI = "mock://localhost:27017/"
        self.REDIS_HOST = "mock_redis"

class MockDatabaseConnections:
    """Mock database connections for demo"""
    
    def __init__(self, config):
        self.config = config
        self.connected = False
        
    async def connect_all(self):
        """Mock connection to all databases"""
        logger.info("🔗 Connecting to mock databases...")
        await asyncio.sleep(1)  # Simulate connection time
        self.connected = True
        logger.info("✅ Mock database connections established")
        
    async def close_all(self):
        """Mock cleanup"""
        logger.info("🧹 Closing mock database connections")
        self.connected = False

class MockStagingLayer:
    """Mock staging layer for demo"""
    
    def __init__(self, db_connections):
        self.db = db_connections
        self.staged_emails = {}
        
    async def ingest_raw_email(self, email_data):
        """Mock email ingestion"""
        email_id = f"mock_{len(self.staged_emails) + 1}"
        self.staged_emails[email_id] = {
            "email_id": email_id,
            "raw_data": email_data,
            "ingestion_timestamp": datetime.now(timezone.utc),
            "processing_status": "pending"
        }
        logger.info(f"✅ Mock email ingested: {email_id}")
        return email_id
        
    async def mark_email_processed(self, email_id):
        """Mock marking email as processed"""
        if email_id in self.staged_emails:
            self.staged_emails[email_id]["processing_status"] = "processed"
            logger.info(f"✅ Mock email marked as processed: {email_id}")

class MockOperationalLayer:
    """Mock operational layer for demo"""
    
    def __init__(self, db_connections):
        self.db = db_connections
        self.stored_records = []
        
    async def initialize_schema(self):
        """Mock schema initialization"""
        logger.info("🏗️ Initializing mock PostgreSQL schema...")
        await asyncio.sleep(0.5)
        logger.info("✅ Mock schema initialized")
        
    async def store_processed_email(self, processed_record):
        """Mock storing processed email"""
        record_id = len(self.stored_records) + 1
        self.stored_records.append({
            "record_id": record_id,
            "email_id": processed_record.email_metadata.email_id,
            "customer_name": processed_record.customer_data.customer_name,
            "phone_numbers": processed_record.customer_data.phone_numbers,
            "urgency_level": processed_record.hvac_context.urgency_level,
            "business_value_score": processed_record.business_value_score,
            "stored_timestamp": datetime.now(timezone.utc)
        })
        logger.info(f"✅ Mock email stored with record_id: {record_id}")
        return record_id

class MockETLPipeline:
    """Mock ETL pipeline for demo"""
    
    def __init__(self, config):
        self.config = config
        self.db_connections = MockDatabaseConnections(config)
        self.staging_layer = MockStagingLayer(self.db_connections)
        self.operational_layer = MockOperationalLayer(self.db_connections)
        
    async def initialize(self):
        """Initialize mock pipeline"""
        logger.info("🚀 Initializing Mock ETL Pipeline...")
        await self.db_connections.connect_all()
        await self.operational_layer.initialize_schema()
        logger.info("✅ Mock ETL Pipeline initialized")
        
    async def process_sample_emails(self):
        """Process sample emails for demo"""
        logger.info("📧 Processing sample Dolores emails...")
        
        # Sample email data
        sample_emails = [
            {
                "subject": "2025-05-21 10:41:04 (phone) Przemek Klima Tech (+48 604 515 878)",
                "from": "<EMAIL>",
                "to": "<EMAIL>",
                "content": "Dzień dobry, dzwonię w sprawie awarii klimatyzacji Daikin. Urządzenie nie chłodzi.",
                "has_attachments": True,
                "m4a_attachments": ["audio_1.m4a"],
                "received_timestamp": datetime.now(timezone.utc)
            },
            {
                "subject": "2025-05-21 14:22:15 (phone) Anna Kowalska (+48 512 345 678)",
                "from": "<EMAIL>", 
                "to": "<EMAIL>",
                "content": "Witam, potrzebuję pilnego serwisu klimatyzacji LG. Split w biurze przestał działać.",
                "has_attachments": True,
                "m4a_attachments": ["audio_2.m4a"],
                "received_timestamp": datetime.now(timezone.utc)
            },
            {
                "subject": "2025-05-21 16:45:30 (phone) Marek Nowak (+48 601 234 567)",
                "from": "<EMAIL>",
                "to": "<EMAIL>", 
                "content": "Proszę o wycenę instalacji nowej klimatyzacji w mieszkaniu 60m2.",
                "has_attachments": False,
                "m4a_attachments": [],
                "received_timestamp": datetime.now(timezone.utc)
            }
        ]
        
        processed_count = 0
        
        for email_data in sample_emails:
            try:
                # Stage 1: Ingest into staging
                email_id = await self.staging_layer.ingest_raw_email(email_data)
                
                # Stage 2: Process and enrich (mock processing)
                processed_record = await self._mock_process_email(email_id, email_data)
                
                # Stage 3: Store in operational database
                record_id = await self.operational_layer.store_processed_email(processed_record)
                
                # Mark as processed
                await self.staging_layer.mark_email_processed(email_id)
                
                processed_count += 1
                logger.info(f"📈 Processed email {processed_count}/3")
                
            except Exception as e:
                logger.error(f"❌ Failed to process email: {e}")
                
        # Generate demo report
        await self._generate_demo_report(processed_count)
        
    async def _mock_process_email(self, email_id, email_data):
        """Mock email processing"""
        
        # Extract phone number from subject
        import re
        phone_match = re.search(r'\+48\s*\d{3}\s*\d{3}\s*\d{3}', email_data['subject'])
        phone_numbers = [phone_match.group()] if phone_match else []
        
        # Extract customer name
        name_match = re.search(r'\)\s*([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+\s+[A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż]+)', email_data['subject'])
        customer_name = name_match.group(1) if name_match else None
        
        # Mock email metadata
        email_metadata = EmailMetadata(
            email_id=email_id,
            message_id=f"msg_{email_id}",
            subject=email_data['subject'],
            sender=email_data['from'],
            recipient=email_data['to'],
            received_timestamp=email_data['received_timestamp'],
            processing_timestamp=datetime.now(timezone.utc),
            raw_content=email_data['content'],
            has_attachments=email_data['has_attachments'],
            attachment_count=len(email_data['m4a_attachments']),
            m4a_attachments=email_data['m4a_attachments']
        )
        
        # Mock customer data
        customer_data = CustomerData(
            phone_numbers=phone_numbers,
            customer_name=customer_name,
            address=None,
            email_contact=email_data['from'],
            contact_source="email_subject",
            confidence_score=0.8 if phone_numbers and customer_name else 0.5
        )
        
        # Mock audio metadata
        audio_metadata = None
        if email_data['m4a_attachments']:
            audio_metadata = AudioMetadata(
                filename=email_data['m4a_attachments'][0],
                file_size=2048000,
                duration_seconds=180.0,
                transcription_text=email_data['content'],
                transcription_confidence=0.85,
                keywords_detected=['klimatyzacja', 'serwis'] if 'serwis' in email_data['content'] else ['klimatyzacja'],
                processing_time=3.2
            )
        
        # Mock HVAC context
        urgency_level = "high" if "pilne" in email_data['content'] or "awaria" in email_data['content'] else "medium"
        equipment_brands = []
        if "daikin" in email_data['content'].lower():
            equipment_brands.append("DAIKIN")
        if "lg" in email_data['content'].lower():
            equipment_brands.append("LG")
            
        service_types = []
        if "awaria" in email_data['content']:
            service_types.append("breakdown")
        elif "serwis" in email_data['content']:
            service_types.append("maintenance")
        elif "instalacja" in email_data['content']:
            service_types.append("installation")
            
        hvac_context = HVACContext(
            equipment_brands=equipment_brands,
            service_types=service_types,
            urgency_level=urgency_level,
            issue_category="cooling" if "chłodzi" in email_data['content'] else "general",
            estimated_priority=8 if urgency_level == "high" else 6
        )
        
        # Calculate business value
        business_value_score = 0.7 if urgency_level == "high" else 0.5
        
        # Mock data lineage
        data_lineage = {
            "processing_timestamp": datetime.now(timezone.utc),
            "extraction_methods": ["regex_phone", "regex_name", "keyword_detection"],
            "confidence_scores": {
                "customer_data": customer_data.confidence_score,
                "transcription": audio_metadata.transcription_confidence if audio_metadata else 0.0
            },
            "processing_stage": "mock_processing_complete"
        }
        
        return ProcessedEmailRecord(
            email_metadata=email_metadata,
            customer_data=customer_data,
            audio_metadata=audio_metadata,
            hvac_context=hvac_context,
            data_lineage=data_lineage,
            business_value_score=business_value_score
        )
        
    async def _generate_demo_report(self, processed_count):
        """Generate demo report"""
        report = {
            "demo_execution": {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "processed_emails": processed_count,
                "demo_mode": True,
                "database_type": "mock",
                "success_rate": 100.0
            },
            "extracted_data": {
                "customers_identified": processed_count,
                "phone_numbers_extracted": processed_count,
                "hvac_requests_created": processed_count,
                "audio_transcriptions": 2  # First 2 emails had audio
            },
            "business_insights": {
                "urgent_requests": 2,
                "equipment_brands_detected": ["DAIKIN", "LG"],
                "service_types": ["breakdown", "maintenance", "installation"]
            },
            "stored_records": self.operational_layer.stored_records
        }
        
        # Save demo report
        report_file = f"demo_pipeline_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
        logger.info(f"📋 Demo report saved: {report_file}")
        
        # Display summary
        logger.info("=" * 60)
        logger.info("🎉 DEMO PIPELINE EXECUTION COMPLETED!")
        logger.info(f"📊 Processed Emails: {processed_count}")
        logger.info(f"👥 Customers Identified: {processed_count}")
        logger.info(f"📞 Phone Numbers Extracted: {processed_count}")
        logger.info(f"🔧 Service Requests Created: {processed_count}")
        logger.info(f"🎤 Audio Transcriptions: 2")
        logger.info("=" * 60)
        
        return report
        
    async def cleanup(self):
        """Cleanup demo resources"""
        await self.db_connections.close_all()
        logger.info("🧹 Demo pipeline cleanup completed")

async def main():
    """Main demo function"""
    logger.info("🎯 DOLORES DATABASE INTEGRATION PIPELINE - DEMO MODE")
    logger.info("=" * 60)
    logger.info("🧪 Running with mock databases and sample data")
    logger.info("=" * 60)
    
    # Initialize demo pipeline
    config = MockDatabaseConfig()
    pipeline = MockETLPipeline(config)
    
    try:
        # Initialize pipeline
        await pipeline.initialize()
        
        # Process sample emails
        await pipeline.process_sample_emails()
        
        logger.info("🎉 Demo completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise
    finally:
        # Cleanup
        await pipeline.cleanup()

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
