{"real_data_processing": {"timestamp": "2025-05-30T21:35:21.100420+00:00", "processed_emails": 10, "archive_source": "dolores_email_archive", "processing_mode": "real_data_extraction"}, "extraction_performance": {"phone_extraction_rate": "0.0%", "name_extraction_rate": "0.0%", "audio_processing_rate": "0.0%"}, "business_metrics": {"high_value_emails": 0, "urgent_requests": 0, "average_business_value": 0.0}, "processed_records": [{"email_id": "mock_1", "record_id": 1, "customer_name": null, "phone_numbers": [], "urgency_level": "low", "business_value_score": 0.0, "has_audio": false}, {"email_id": "mock_2", "record_id": 2, "customer_name": null, "phone_numbers": [], "urgency_level": "low", "business_value_score": 0.0, "has_audio": false}, {"email_id": "mock_3", "record_id": 3, "customer_name": null, "phone_numbers": [], "urgency_level": "low", "business_value_score": 0.0, "has_audio": false}, {"email_id": "mock_4", "record_id": 4, "customer_name": null, "phone_numbers": [], "urgency_level": "low", "business_value_score": 0.0, "has_audio": false}, {"email_id": "mock_5", "record_id": 5, "customer_name": null, "phone_numbers": [], "urgency_level": "low", "business_value_score": 0.0, "has_audio": false}]}